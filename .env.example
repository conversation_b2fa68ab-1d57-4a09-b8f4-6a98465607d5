# 服务器配置
PORT=3000
NODE_ENV=development

# 浏览器配置
# 浏览器类型：chromium 或 camoufox (默认: camoufox)
BROWSER_TYPE=chromium
HEADLESS=false
USER_AGENT=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
 

# 浏览器可执行文件路径 (可选)
# 对于 chromium
PLAYWRIGHT_EXECUTABLE_PATH=/usr/bin/chromium-browser
# 对于 camoufox 
#CAMOUFOX_EXECUTABLE_PATH=/usr/local/bin/camoufox

 
# CORS 配置
CORS_ORIGIN=*

# API 认证配置
# 设置一个强密码作为API访问令牌
API_TOKEN=your_secret_api_token_here
PROOF_GODGODGAME_TOKEN=your_secret_api_token_here
PROOF_IGIVEN_TOKEN=your_secret_api_token_here