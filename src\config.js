import dotenv from 'dotenv';

// 加载环境变量文件
// 先加载 .env 文件（默认配置）
dotenv.config({ path: '.env' });
// 再加载 .env.local 文件（本地覆盖配置，优先级更高）
dotenv.config({ path: '.env.local' });

// 浏览器类型配置，支持 chromium、camoufox、patchright
const browserType = process.env.BROWSER_TYPE || 'chromium';

/**
 * 应用程序配置对象
 * 包含服务器、浏览器和应用程序的所有配置选项
 *
 * @typedef {Object} Config
 * @property {Object} server - 服务器配置
 * @property {number} server.port - 服务器端口号，默认 7860
 * @property {string} server.host - 服务器主机地址，默认 '0.0.0.0'
 *
 * @property {Object} browser - Playwright 浏览器配置
 * @property {string} browser.type - 浏览器类型：'chromium'、'camoufox' 或 'patchright'
 * @property {boolean} browser.headless - 是否以无头模式运行浏览器，默认 true
 * @property {number} browser.timeout - 浏览器操作超时时间（毫秒），默认 60000
 * @property {string|undefined} browser.executablePath - 浏览器可执行文件路径，camoufox 时为 undefined
 * @property {string[]} browser.args - 浏览器启动参数数组
 *
 * @property {Object} app - 应用程序配置
 * @property {string} app.token - API 访问令牌，用于身份验证
 */
const config = {
  // 服务器配置
  server: {
    /** @type {number} 服务器监听端口，从环境变量 PORT 读取，默认 7860 */
    port: parseInt(process.env.PORT || '7860', 10),
    /** @type {string} 服务器绑定的主机地址，从环境变量 HOST 读取，默认 '0.0.0.0' */
    host: process.env.HOST || '0.0.0.0'
  },

  // Playwright 浏览器配置
  browser: {
    /** @type {string} 浏览器类型：'chromium'、'camoufox' 或 'patchright' */
    type: browserType,
    /** @type {boolean} 是否以无头模式运行浏览器，从环境变量 HEADLESS 读取，默认 true */
    headless: (process.env.HEADLESS || 'true').toLowerCase() !== 'false',
    /** @type {number} 浏览器操作超时时间（毫秒），从环境变量 TIMEOUT 读取，默认 60000 */
    timeout: parseInt(process.env.TIMEOUT || '60000', 10),
    /**
     * @type {string|undefined} 浏览器可执行文件路径
     * - camoufox: 返回 undefined，由 camoufox.js 自动处理
     * - chromium/patchright: 从环境变量 PLAYWRIGHT_EXECUTABLE_PATH 读取
     */
    executablePath: (() => {
      if (browserType === 'camoufox') {
        // camoufox.js会使用os.homedir()获取用户主目录
        return undefined;
      }
      return process.env.PLAYWRIGHT_EXECUTABLE_PATH;
    })(),

    /**
     * @type {string[]} 浏览器启动参数数组
     * 根据不同浏览器类型返回相应的启动参数：
     * - chromium: 包含防检测参数
     * - camoufox: Docker环境优化参数
     * - patchright: 基础参数（自动处理防检测）
     */
    args: (() => {
      const baseArgs = [
        '--no-sandbox',
        '--disable-dev-shm-usage',
        '--disable-infobars',
        '--disable-extensions',
      ];

      // 如果是 chromium，添加防检测参数
      if (browserType === 'chromium') {
        return [
          '--disable-blink-features=AutomationControlled',
          ...baseArgs
        ];
      }
      // camoufox Docker环境优化参数
      if (browserType === 'camoufox') {
        return [
          ...baseArgs
        ];
      }
      // patchright 使用默认参数（Patchright 会自动处理防检测）
      if (browserType === 'patchright') {
        return baseArgs;
      }
      return baseArgs;
    })()
  },

  // 应用程序配置
  app: {
    /** @type {string} API 访问令牌，用于身份验证，从环境变量 API_TOKEN 读取 */
    token: process.env.API_TOKEN,
    proof: [
      {
        "suffix": "godgodgame.com",
        "apiUrl": "https://seedmail.igiven.com/api/latest-email",
        "token": process.env.PROOF_GODGODGAME_TOKEN
      },
      {
        "suffix": "igiven.com",
        "apiUrl": "https://mail.igiven.com/api/latest-email",
        "token": process.env.PROOF_IGIVEN_TOKEN
      }
    ]
  },
};

export default config;