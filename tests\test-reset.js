// 测试重置路由的简单脚本
import config from '../src/config.js';

const testAccounts = [
    {
        id: 1,
        email: "<EMAIL>",
        password: "testpassword",
        proofEmail: "<EMAIL>",
        createDatetime: "",
        resetStatus: 0,
        resetDatetime: "",
        resetFailMsg: "",
        initStatus: 0,
        initDatetime: "",
        initFailMsg: ""
    }
];

async function testResetEndpoint() {
    try {
        const response = await fetch('http://localhost:3096/reset-account', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${config.app.token}`
            },
            body: JSON.stringify(testAccounts)
        });

        const result = await response.json();
        console.log('Response Status:', response.status);
        console.log('Response:', JSON.stringify(result, null, 2));
    } catch (error) {
        console.error('Test failed:', error.message);
    }
}

// 执行测试
console.log('Testing reset endpoint...');
testResetEndpoint();
